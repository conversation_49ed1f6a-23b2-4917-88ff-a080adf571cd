import { 
  CalendarEvent, 
  CalendarSettings, 
  SyncResult, 
  CreateCalendarEventData,
  UpdateCalendarEventData 
} from '../types/calendar';
import { DiagnosticRecord } from '../types';
import { googleCalendarService, GoogleCalendarEvent } from './googleCalendar';
import { 
  getCalendarEvents, 
  addCalendarEvent, 
  updateCalendarEvent, 
  deleteCalendarEvent,
  getOrCreateCalendarSettings,
  updateCalendarSettings,
  getDiagnosticRecords
} from './api';
import { auth } from './api';

/**
 * Service de synchronisation bidirectionnelle entre Firebase et Google Calendar
 * Gère la création automatique d'événements basés sur les diagnostics et la synchronisation
 */
class CalendarSyncService {
  private syncInProgress = false;

  /**
   * Synchronise les événements Firebase vers Google Calendar
   */
  async syncFirebaseToGoogle(userId: string): Promise<Partial<SyncResult>> {
    try {
      const settings = await getOrCreateCalendarSettings(userId);
      let syncedEvents = 0;
      let createdEvents = 0;
      let updatedEvents = 0;
      const errors: string[] = [];

      // Récupération des événements Firebase non synchronisés ou modifiés
      return new Promise((resolve) => {
        const unsubscribe = getCalendarEvents(userId, async (firebaseEvents) => {
          unsubscribe(); // Arrêter l'écoute après la première récupération

          for (const event of firebaseEvents) {
            try {
              if (!event.googleCalendarEventId) {
                // Créer l'événement dans Google Calendar
                const googleEvent = await googleCalendarService.createTreatmentEvent(
                  event.plantId,
                  event.plantName,
                  event.treatmentType,
                  event.startDate,
                  event.description,
                  settings.defaultCalendarId
                );

                // Mettre à jour l'événement Firebase avec l'ID Google
                await updateCalendarEvent(userId, event.id, {
                  googleCalendarEventId: googleEvent.id
                });

                createdEvents++;
              } else {
                // Mettre à jour l'événement existant dans Google Calendar
                await googleCalendarService.updateEvent(
                  settings.defaultCalendarId,
                  event.googleCalendarEventId,
                  {
                    summary: event.title,
                    description: event.description,
                    start: { dateTime: event.startDate.toISOString() },
                    end: { dateTime: event.endDate.toISOString() },
                    plantId: event.plantId,
                    treatmentType: event.treatmentType
                  }
                );

                updatedEvents++;
              }
              syncedEvents++;
            } catch (error) {
              console.error(`Erreur lors de la synchronisation de l'événement ${event.id}:`, error);
              errors.push(`Événement ${event.title}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
            }
          }

          resolve({
            syncedEvents,
            createdEvents,
            updatedEvents,
            errors
          });
        });
      });
    } catch (error) {
      console.error('Erreur lors de la synchronisation Firebase vers Google:', error);
      throw error;
    }
  }

  /**
   * Synchronise les événements Google Calendar vers Firebase
   */
  async syncGoogleToFirebase(userId: string): Promise<Partial<SyncResult>> {
    try {
      const settings = await getOrCreateCalendarSettings(userId);
      let syncedEvents = 0;
      let createdEvents = 0;
      let updatedEvents = 0;
      const errors: string[] = [];

      // Récupération des événements Google Calendar
      const googleEvents = await googleCalendarService.getEvents(
        settings.defaultCalendarId,
        new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours dans le passé
        new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)  // 1 an dans le futur
      );

      // Récupération des événements Firebase existants
      return new Promise((resolve) => {
        const unsubscribe = getCalendarEvents(userId, async (firebaseEvents) => {
          unsubscribe();

          const firebaseEventsByGoogleId = new Map(
            firebaseEvents
              .filter(e => e.googleCalendarEventId)
              .map(e => [e.googleCalendarEventId!, e])
          );

          for (const googleEvent of googleEvents) {
            try {
              // Vérifier si c'est un événement de traitement de plante
              if (!googleEvent.plantId || !googleEvent.treatmentType) {
                continue; // Ignorer les événements non liés aux plantes
              }

              const existingFirebaseEvent = firebaseEventsByGoogleId.get(googleEvent.id!);

              if (!existingFirebaseEvent) {
                // Créer un nouvel événement Firebase
                const eventData: CreateCalendarEventData = {
                  plantId: googleEvent.plantId,
                  plantName: googleEvent.summary.split(' - ')[1] || 'Plante inconnue',
                  treatmentType: googleEvent.treatmentType as any,
                  title: googleEvent.summary,
                  description: googleEvent.description,
                  startDate: new Date(googleEvent.start.dateTime),
                  endDate: new Date(googleEvent.end.dateTime),
                  isRecurring: Boolean(googleEvent.recurrence?.length),
                  createdBy: 'system'
                };

                const docRef = await addCalendarEvent(userId, eventData);
                
                // Mettre à jour avec l'ID Google Calendar
                await updateCalendarEvent(userId, docRef.id, {
                  googleCalendarEventId: googleEvent.id
                });

                createdEvents++;
              } else {
                // Mettre à jour l'événement Firebase existant
                const updateData: UpdateCalendarEventData = {
                  title: googleEvent.summary,
                  description: googleEvent.description,
                  startDate: new Date(googleEvent.start.dateTime),
                  endDate: new Date(googleEvent.end.dateTime),
                  isRecurring: Boolean(googleEvent.recurrence?.length)
                };

                await updateCalendarEvent(userId, existingFirebaseEvent.id, updateData);
                updatedEvents++;
              }
              syncedEvents++;
            } catch (error) {
              console.error(`Erreur lors de la synchronisation de l'événement Google ${googleEvent.id}:`, error);
              errors.push(`Événement Google ${googleEvent.summary}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
            }
          }

          resolve({
            syncedEvents,
            createdEvents,
            updatedEvents,
            errors
          });
        });
      });
    } catch (error) {
      console.error('Erreur lors de la synchronisation Google vers Firebase:', error);
      throw error;
    }
  }

  /**
   * Synchronisation bidirectionnelle complète
   */
  async fullSync(userId: string): Promise<SyncResult> {
    if (this.syncInProgress) {
      throw new Error('Une synchronisation est déjà en cours');
    }

    this.syncInProgress = true;
    const startTime = new Date();

    try {
      // Synchronisation Firebase vers Google
      const firebaseToGoogleResult = await this.syncFirebaseToGoogle(userId);
      
      // Synchronisation Google vers Firebase
      const googleToFirebaseResult = await this.syncGoogleToFirebase(userId);

      // Mise à jour de la date de dernière synchronisation
      await updateCalendarSettings(userId, {
        lastSyncDate: startTime
      });

      const result: SyncResult = {
        success: true,
        message: 'Synchronisation terminée avec succès',
        syncedEvents: (firebaseToGoogleResult.syncedEvents || 0) + (googleToFirebaseResult.syncedEvents || 0),
        createdEvents: (firebaseToGoogleResult.createdEvents || 0) + (googleToFirebaseResult.createdEvents || 0),
        updatedEvents: (firebaseToGoogleResult.updatedEvents || 0) + (googleToFirebaseResult.updatedEvents || 0),
        deletedEvents: 0, // À implémenter si nécessaire
        errors: [...(firebaseToGoogleResult.errors || []), ...(googleToFirebaseResult.errors || [])],
        timestamp: startTime
      };

      return result;
    } catch (error) {
      console.error('Erreur lors de la synchronisation complète:', error);
      return {
        success: false,
        message: `Erreur lors de la synchronisation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        syncedEvents: 0,
        createdEvents: 0,
        updatedEvents: 0,
        deletedEvents: 0,
        errors: [error instanceof Error ? error.message : 'Erreur inconnue'],
        timestamp: startTime
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Crée automatiquement des événements de calendrier basés sur les diagnostics
   */
  async createEventsFromDiagnostics(userId: string, plantId: string): Promise<number> {
    try {
      let createdEventsCount = 0;

      return new Promise((resolve) => {
        const unsubscribe = getDiagnosticRecords(userId, plantId, async (diagnostics) => {
          unsubscribe();

          for (const diagnostic of diagnostics) {
            try {
              if (diagnostic.nextTreatmentDate && diagnostic.diagnosis.treatmentPlan) {
                const treatmentDate = diagnostic.nextTreatmentDate.toDate();
                const now = new Date();

                // Ne créer des événements que pour les traitements futurs
                if (treatmentDate > now) {
                  const eventData: CreateCalendarEventData = {
                    plantId: diagnostic.plantId,
                    plantName: `Plante ${diagnostic.plantId}`, // À améliorer avec le nom réel
                    treatmentType: 'traitement',
                    title: `Traitement - ${diagnostic.diagnosis.disease}`,
                    description: `Traitement recommandé pour ${diagnostic.diagnosis.disease}\n\nÉtapes:\n${diagnostic.diagnosis.treatmentPlan.steps.join('\n')}`,
                    startDate: treatmentDate,
                    endDate: new Date(treatmentDate.getTime() + 60 * 60 * 1000), // 1 heure plus tard
                    isRecurring: diagnostic.diagnosis.treatmentPlan.treatmentFrequencyDays > 0,
                    recurrencePattern: diagnostic.diagnosis.treatmentPlan.treatmentFrequencyDays > 0 ? {
                      frequency: 'daily',
                      interval: diagnostic.diagnosis.treatmentPlan.treatmentFrequencyDays
                    } : undefined,
                    createdBy: 'gemini'
                  };

                  await addCalendarEvent(userId, eventData);
                  createdEventsCount++;
                }
              }
            } catch (error) {
              console.error(`Erreur lors de la création d'événement pour le diagnostic ${diagnostic.id}:`, error);
            }
          }

          resolve(createdEventsCount);
        });
      });
    } catch (error) {
      console.error('Erreur lors de la création d\'événements depuis les diagnostics:', error);
      return 0;
    }
  }

  /**
   * Vérifie si une synchronisation automatique est nécessaire
   */
  async shouldAutoSync(userId: string): Promise<boolean> {
    try {
      const settings = await getOrCreateCalendarSettings(userId);
      
      if (!settings.autoSyncEnabled) {
        return false;
      }

      const now = new Date();
      const lastSync = settings.lastSyncDate;
      const syncIntervalMs = settings.syncInterval * 60 * 1000; // Conversion en millisecondes

      return (now.getTime() - lastSync.getTime()) >= syncIntervalMs;
    } catch (error) {
      console.error('Erreur lors de la vérification de la synchronisation automatique:', error);
      return false;
    }
  }
}

// Instance singleton du service
export const calendarSyncService = new CalendarSyncService();
