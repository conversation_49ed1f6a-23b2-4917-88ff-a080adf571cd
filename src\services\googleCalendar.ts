import { auth } from './api';

/**
 * Interface pour un événement de calendrier Google
 */
export interface GoogleCalendarEvent {
  id?: string;
  summary: string;
  description: string;
  start: { dateTime: string };
  end: { dateTime: string };
  plantId: string;
  treatmentType: string;
  recurrence?: string[];
}

/**
 * Interface pour la réponse de l'API Google Calendar
 */
interface GoogleCalendarResponse {
  items: GoogleCalendarEvent[];
  nextPageToken?: string;
}

/**
 * Service d'intégration avec l'API Google Calendar
 * Gère l'authentification, la création, la lecture, la mise à jour et la suppression d'événements
 */
class GoogleCalendarService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://www.googleapis.com/calendar/v3';
  private readonly scopes = [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events'
  ];

  constructor() {
    this.apiKey = import.meta.env.VITE_GOOGLE_CALENDAR_API_KEY;
    if (!this.apiKey) {
      throw new Error('VITE_GOOGLE_CALENDAR_API_KEY n\'est pas définie dans les variables d\'environnement');
    }
  }

  /**
   * Obtient le token d'accès Google de l'utilisateur authentifié
   */
  private async getAccessToken(): Promise<string> {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    try {
      // Récupération du token d'accès Google depuis Firebase Auth
      const token = await user.getIdToken();
      return token;
    } catch (error) {
      console.error('Erreur lors de la récupération du token d\'accès:', error);
      throw new Error('Impossible de récupérer le token d\'accès Google');
    }
  }

  /**
   * Effectue une requête authentifiée vers l'API Google Calendar
   */
  private async makeAuthenticatedRequest(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any
  ): Promise<any> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Erreur API Google Calendar: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Erreur lors de la requête Google Calendar:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des calendriers de l'utilisateur
   */
  async getCalendars(): Promise<any[]> {
    try {
      const response = await this.makeAuthenticatedRequest('/users/me/calendarList');
      return response.items || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des calendriers:', error);
      throw new Error('Impossible de récupérer la liste des calendriers');
    }
  }

  /**
   * Récupère les événements d'un calendrier spécifique
   */
  async getEvents(
    calendarId: string = 'primary',
    timeMin?: Date,
    timeMax?: Date,
    maxResults: number = 250
  ): Promise<GoogleCalendarEvent[]> {
    try {
      const params = new URLSearchParams({
        maxResults: maxResults.toString(),
        singleEvents: 'true',
        orderBy: 'startTime',
      });

      if (timeMin) {
        params.append('timeMin', timeMin.toISOString());
      }
      if (timeMax) {
        params.append('timeMax', timeMax.toISOString());
      }

      const response = await this.makeAuthenticatedRequest(
        `/calendars/${encodeURIComponent(calendarId)}/events?${params.toString()}`
      );

      return response.items || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des événements:', error);
      throw new Error('Impossible de récupérer les événements du calendrier');
    }
  }

  /**
   * Crée un nouvel événement dans le calendrier
   */
  async createEvent(
    calendarId: string = 'primary',
    eventData: Omit<GoogleCalendarEvent, 'id'>
  ): Promise<GoogleCalendarEvent> {
    try {
      const response = await this.makeAuthenticatedRequest(
        `/calendars/${encodeURIComponent(calendarId)}/events`,
        'POST',
        eventData
      );

      return response;
    } catch (error) {
      console.error('Erreur lors de la création de l\'événement:', error);
      throw new Error('Impossible de créer l\'événement dans le calendrier');
    }
  }

  /**
   * Met à jour un événement existant
   */
  async updateEvent(
    calendarId: string = 'primary',
    eventId: string,
    eventData: Partial<GoogleCalendarEvent>
  ): Promise<GoogleCalendarEvent> {
    try {
      const response = await this.makeAuthenticatedRequest(
        `/calendars/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(eventId)}`,
        'PUT',
        eventData
      );

      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'événement:', error);
      throw new Error('Impossible de mettre à jour l\'événement');
    }
  }

  /**
   * Supprime un événement du calendrier
   */
  async deleteEvent(
    calendarId: string = 'primary',
    eventId: string
  ): Promise<void> {
    try {
      await this.makeAuthenticatedRequest(
        `/calendars/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(eventId)}`,
        'DELETE'
      );
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'événement:', error);
      throw new Error('Impossible de supprimer l\'événement');
    }
  }

  /**
   * Crée un événement de traitement pour une plante
   */
  async createTreatmentEvent(
    plantId: string,
    plantName: string,
    treatmentType: string,
    startDate: Date,
    description: string,
    calendarId: string = 'primary'
  ): Promise<GoogleCalendarEvent> {
    const endDate = new Date(startDate.getTime() + 60 * 60 * 1000); // 1 heure plus tard

    const eventData: Omit<GoogleCalendarEvent, 'id'> = {
      summary: `${treatmentType} - ${plantName}`,
      description: `Traitement pour ${plantName}\n\n${description}`,
      start: { dateTime: startDate.toISOString() },
      end: { dateTime: endDate.toISOString() },
      plantId,
      treatmentType,
    };

    return await this.createEvent(calendarId, eventData);
  }

  /**
   * Crée un événement récurrent pour les traitements réguliers
   */
  async createRecurringTreatmentEvent(
    plantId: string,
    plantName: string,
    treatmentType: string,
    startDate: Date,
    description: string,
    frequencyDays: number,
    endDate?: Date,
    calendarId: string = 'primary'
  ): Promise<GoogleCalendarEvent> {
    const eventEndDate = new Date(startDate.getTime() + 60 * 60 * 1000); // 1 heure plus tard
    
    // Création de la règle de récurrence RRULE
    let recurrenceRule = `RRULE:FREQ=DAILY;INTERVAL=${frequencyDays}`;
    if (endDate) {
      recurrenceRule += `;UNTIL=${endDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`;
    }

    const eventData: Omit<GoogleCalendarEvent, 'id'> = {
      summary: `${treatmentType} - ${plantName}`,
      description: `Traitement récurrent pour ${plantName}\n\n${description}`,
      start: { dateTime: startDate.toISOString() },
      end: { dateTime: eventEndDate.toISOString() },
      plantId,
      treatmentType,
      recurrence: [recurrenceRule],
    };

    return await this.createEvent(calendarId, eventData);
  }
}

// Instance singleton du service
export const googleCalendarService = new GoogleCalendarService();
