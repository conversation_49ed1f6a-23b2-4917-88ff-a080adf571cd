import React, { lazy, Suspense } from 'react';
import { HashRouter, Routes, Route, Navigate, Link } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import { useAuth } from '@/hooks/useAuth';
import { signOutUser } from '@/services/api';
import { Spinner } from '@/components/common/Spinner';
import { LogoutIcon, LeafIcon } from '@/components/common/icons';

// Lazy load screen components
const LoginScreen = lazy(() => import('@/components/features/LoginScreen'));
const DashboardScreen = lazy(() => import('@/components/features/DashboardScreen'));
const PlantDetailScreen = lazy(() => import('@/components/features/PlantDetailScreen'));
const CalendarView = lazy(() => import('@/components/features/Calendar/CalendarView'));

const Header: React.FC = () => {
    const { user } = useAuth();
    
    return (
        <header className="p-4 bg-[#1c1a31]/50 backdrop-blur-lg sticky top-0 z-40">
            <div className="container mx-auto flex justify-between items-center">
                <Link to="/" className="flex items-center gap-2">
                    <LeafIcon className="w-8 h-8 text-[#d385f5]" />
                    <span className="text-2xl font-bold text-white">FloraSynth</span>
                </Link>
                {user && (
                    <div className="flex items-center gap-4">
                        <nav className="flex items-center gap-4">
                            <Link
                                to="/"
                                className="text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                Mes Plantes
                            </Link>
                            <Link
                                to="/calendar"
                                className="text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                Calendrier
                            </Link>
                        </nav>
                        <span className="text-sm text-gray-300 hidden sm:block">Bienvenue, {user.displayName || user.email}</span>
                        <button onClick={signOutUser} className="p-2 rounded-full hover:bg-white/10 transition-colors">
                           <LogoutIcon className="w-6 h-6 text-gray-300" />
                        </button>
                    </div>
                )}
            </div>
        </header>
    );
}

const ProtectedLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { user, loading } = useAuth();

    if (loading) {
        return <div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div>;
    }
    
    if (!user) {
        return <Navigate to="/login" replace />;
    }

    return (
        <>
            <Header />
            <main>
                {children}
            </main>
        </>
    );
};


const App: React.FC = () => {
  return (
    <AuthProvider>
      <HashRouter>
        <Suspense fallback={<div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div>}>
          <Routes>
            <Route path="/login" element={<LoginScreen />} />
            <Route path="/" element={<ProtectedLayout><DashboardScreen /></ProtectedLayout>} />
            <Route path="/plant/:plantId" element={<ProtectedLayout><PlantDetailScreen /></ProtectedLayout>} />
            <Route path="/calendar" element={<ProtectedLayout><CalendarView /></ProtectedLayout>} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </HashRouter>
    </AuthProvider>
  );
};

export default App;