
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.** 



<PERSON>h mais attends, c'est de la bombe, regarde la capture. Mais on n'a même pas besoin de Google Agenda, on n'a pas besoin de calendrier. Regarde, dans la capture, c'est marqué prochain traitement dans 15 jours. Alors je ne sais pas si c'est le calendrier qui fait ça, mais si ce n'est pas le calendrier, alors du coup, il n'y a pas besoin. Je vais te dire pourquoi : on va marcher avec des notifications dans l'application pour chaque utilisateur.

T'as l'idée, elle est géniale. C'est vraiment de la bombe. En fait, ce qu'il faut faire, c'est... Je t'explique : à chaque fois qu'on crée un objet pour une plante, pour créer un diagnostic, il faut d'abord la date et l'heure précises à chaque fois qu'on crée un événement. Et puis ensuite, Gemini va chercher dans sa base de données et il dit : voilà, dans X jours, X semaines, X mois, etc. Et tenir un journal de ce que l'utilisateur a fait. On n'a même pas besoin de Google Agenda pour ça. 

Il faut comparer les deux en fait. Mon idée, il faut que tu la compares avec Google, le calendrier. Je ne sais pas si c'est Google Agenda, mais je suppose. Mais compare les deux : c'est quoi qui prévaut ? Est-ce que c'est mon idée ? Est-ce que c'est mon idée avec des notifications dans l'application et tenir un journal ou s'embêter la vie avec des réglages à plus en finir avec un calendrier synchronisé avec Google Agenda et je ne sais pas quoi, Oui, l'API, qu'est-ce que t'en penses ? Moi, je pense que mon idée, elle est pas mauvaise. 
























