# **TESTS D'INTÉGRATION GOOGLE CALENDAR - PHASE 1**

## **OBJECTIF DES TESTS**

Ce document définit les procédures de test pour valider l'intégration Google Calendar avec l'application FloraSynth. Les tests couvrent la synchronisation bidirectionnelle, l'interface utilisateur et les fonctionnalités de base.

---

## **1. PRÉ-REQUIS POUR LES TESTS**

### **1.1 Dépendances à Installer**

**Packages manquants à installer :**
```bash
npm install clsx tailwind-merge class-variance-authority @radix-ui/react-slot
```

### **1.2 Configuration Requise**

- ✅ Variables d'environnement configurées (`.env.local`)
- ✅ Authentification Google avec scopes Calendar
- ✅ Projet Firebase configuré
- ✅ Accès à Google Calendar API

### **1.3 Données de Test**

- Au moins 2 plantes créées dans l'application
- Au moins 1 diagnostic avec traitement programmé
- Accès à un compte Google Calendar de test

---

## **2. TESTS FONCTIONNELS**

### **2.1 Test d'Authentification et Permissions**

**Objectif :** Vérifier que l'authentification Google inclut les permissions Calendar

**Procédure :**
1. Se déconnecter de l'application
2. Se reconnecter via Google Sign-In
3. Vérifier que les scopes Calendar sont demandés
4. Confirmer l'accès aux calendriers Google

**Résultat attendu :**
- ✅ Permissions Calendar accordées
- ✅ Accès aux calendriers Google disponible
- ✅ Aucune erreur d'authentification

### **2.2 Test de Navigation vers le Calendrier**

**Objectif :** Vérifier l'accès à la vue calendrier

**Procédure :**
1. Depuis le dashboard, cliquer sur "Calendrier" dans la navigation
2. Vérifier le chargement de la page calendrier
3. Tester la navigation retour vers "Mes Plantes"

**Résultat attendu :**
- ✅ Page calendrier accessible via `/calendar`
- ✅ Interface calendrier s'affiche correctement
- ✅ Navigation bidirectionnelle fonctionnelle

### **2.3 Test de Création d'Événement Manuel**

**Objectif :** Valider la création manuelle d'événements

**Procédure :**
1. Cliquer sur "Nouvel événement" dans la vue calendrier
2. Remplir le formulaire :
   - Sélectionner une plante existante
   - Choisir "Traitement" comme type
   - Définir titre et description
   - Programmer pour demain à 10h00
3. Sauvegarder l'événement
4. Vérifier l'affichage dans la liste

**Résultat attendu :**
- ✅ Modal de création s'ouvre correctement
- ✅ Formulaire validé et sauvegardé
- ✅ Événement visible dans la vue calendrier
- ✅ Données correctement stockées dans Firebase

### **2.4 Test de Synchronisation Firebase → Google Calendar**

**Objectif :** Valider la synchronisation vers Google Calendar

**Procédure :**
1. Créer un événement dans l'application (test 2.3)
2. Cliquer sur "Synchroniser" dans la vue calendrier
3. Attendre la fin de la synchronisation
4. Vérifier dans Google Calendar web que l'événement apparaît
5. Contrôler les détails de l'événement (titre, description, date)

**Résultat attendu :**
- ✅ Synchronisation réussie sans erreur
- ✅ Événement créé dans Google Calendar
- ✅ Détails corrects (titre, description, horaire)
- ✅ ID Google Calendar stocké dans Firebase

### **2.5 Test de Synchronisation Google Calendar → Firebase**

**Objectif :** Valider la synchronisation depuis Google Calendar

**Procédure :**
1. Créer manuellement un événement dans Google Calendar web :
   - Titre : "Test Sync - [Nom Plante]"
   - Description : Inclure `plantId: [ID_PLANTE_EXISTANTE]` et `treatmentType: traitement`
2. Dans l'application, cliquer sur "Synchroniser"
3. Vérifier que l'événement apparaît dans la vue calendrier
4. Contrôler les détails importés

**Résultat attendu :**
- ✅ Événement Google importé dans Firebase
- ✅ Affichage correct dans l'interface
- ✅ Métadonnées correctement interprétées
- ✅ Pas de doublons créés

### **2.6 Test de Création Automatique depuis Diagnostics**

**Objectif :** Valider la création automatique d'événements basés sur les diagnostics

**Procédure :**
1. Créer un diagnostic avec traitement programmé pour une plante
2. Utiliser la fonction `createEventsFromDiagnostics` du service de sync
3. Vérifier la création automatique d'événements
4. Contrôler la récurrence si applicable

**Résultat attendu :**
- ✅ Événements créés automatiquement
- ✅ Dates de traitement correctes
- ✅ Récurrence configurée selon la fréquence
- ✅ Métadonnées Gemini préservées

---

## **3. TESTS D'INTERFACE UTILISATEUR**

### **3.1 Test de Responsivité**

**Objectif :** Vérifier l'affichage sur différentes tailles d'écran

**Procédure :**
1. Tester sur desktop (1920x1080)
2. Tester sur tablette (768x1024)
3. Tester sur mobile (375x667)
4. Vérifier tous les composants (CalendarView, EventModal, CalendarSettings)

**Résultat attendu :**
- ✅ Interface adaptée à toutes les tailles
- ✅ Pas de débordement horizontal
- ✅ Boutons et formulaires utilisables
- ✅ Navigation tactile fonctionnelle

### **3.2 Test des Vues de Calendrier**

**Objectif :** Valider les différentes vues (jour, semaine, mois, liste)

**Procédure :**
1. Créer plusieurs événements sur différentes dates
2. Tester chaque vue (jour, semaine, mois, liste)
3. Vérifier la navigation entre périodes
4. Contrôler l'affichage des événements

**Résultat attendu :**
- ✅ Toutes les vues fonctionnelles
- ✅ Navigation temporelle correcte
- ✅ Événements affichés aux bonnes dates
- ✅ Filtrage par vue opérationnel

### **3.3 Test des Paramètres de Calendrier**

**Objectif :** Valider la configuration des paramètres

**Procédure :**
1. Ouvrir les paramètres de calendrier
2. Modifier le calendrier par défaut
3. Configurer les notifications
4. Ajuster la synchronisation automatique
5. Sauvegarder et vérifier la persistance

**Résultat attendu :**
- ✅ Interface de paramètres accessible
- ✅ Modifications sauvegardées
- ✅ Paramètres appliqués correctement
- ✅ Calendriers Google listés

---

## **4. TESTS DE PERFORMANCE ET ROBUSTESSE**

### **4.1 Test de Charge de Synchronisation**

**Objectif :** Valider les performances avec de nombreux événements

**Procédure :**
1. Créer 50+ événements dans Firebase
2. Lancer une synchronisation complète
3. Mesurer le temps de traitement
4. Vérifier l'absence d'erreurs

**Résultat attendu :**
- ✅ Synchronisation terminée en < 30 secondes
- ✅ Aucune erreur de timeout
- ✅ Interface reste responsive
- ✅ Tous les événements synchronisés

### **4.2 Test de Gestion d'Erreurs**

**Objectif :** Valider la robustesse face aux erreurs

**Procédure :**
1. Tester sans connexion internet
2. Tester avec permissions Calendar révoquées
3. Tester avec calendrier Google supprimé
4. Vérifier les messages d'erreur utilisateur

**Résultat attendu :**
- ✅ Messages d'erreur clairs et utiles
- ✅ Application ne plante pas
- ✅ Récupération gracieuse possible
- ✅ État de l'application préservé

---

## **5. VALIDATION FINALE**

### **5.1 Checklist de Validation Complète**

**Fonctionnalités Core :**
- [ ] Authentification Google avec scopes Calendar
- [ ] Navigation vers la vue calendrier
- [ ] Création manuelle d'événements
- [ ] Synchronisation bidirectionnelle
- [ ] Création automatique depuis diagnostics
- [ ] Gestion des paramètres utilisateur

**Interface Utilisateur :**
- [ ] Responsivité sur tous les appareils
- [ ] Toutes les vues de calendrier fonctionnelles
- [ ] Modals et formulaires opérationnels
- [ ] Messages de feedback appropriés

**Performance et Robustesse :**
- [ ] Synchronisation performante (< 30s pour 50+ événements)
- [ ] Gestion d'erreurs gracieuse
- [ ] Pas de fuites mémoire
- [ ] Interface reste responsive

### **5.2 Critères de Succès Phase 1**

**✅ SUCCÈS si :**
- Toutes les fonctionnalités core opérationnelles
- Interface utilisateur intuitive et responsive
- Synchronisation bidirectionnelle fiable
- Aucune régression sur fonctionnalités existantes
- Performance acceptable (< 30s sync, interface fluide)

**❌ ÉCHEC si :**
- Synchronisation non fonctionnelle
- Interface inutilisable sur mobile
- Erreurs critiques non gérées
- Régression sur fonctionnalités existantes

---

## **6. RAPPORT DE TEST**

### **Template de Rapport**

```markdown
# Rapport de Test - Intégration Google Calendar

**Date :** [DATE]
**Testeur :** [NOM]
**Version :** Phase 1

## Résultats par Catégorie

### Fonctionnalités Core
- Authentification : ✅/❌
- Navigation : ✅/❌
- Création événements : ✅/❌
- Synchronisation : ✅/❌

### Interface Utilisateur
- Responsivité : ✅/❌
- Vues calendrier : ✅/❌
- Paramètres : ✅/❌

### Performance
- Temps de sync : [X] secondes
- Charge supportée : [X] événements
- Erreurs rencontrées : [LISTE]

## Recommandations
[LISTE DES AMÉLIORATIONS SUGGÉRÉES]

## Validation Finale
SUCCÈS ✅ / ÉCHEC ❌
```

---

**🎯 OBJECTIF PHASE 1 : Validation complète de l'intégration Google Calendar avec synchronisation bidirectionnelle fonctionnelle et interface utilisateur intuitive.**
