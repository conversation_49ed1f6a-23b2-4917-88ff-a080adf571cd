import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { CalendarSettings } from '@/types/calendar';
import { getOrCreateCalendarSettings, updateCalendarSettings } from '@/services/api';
import { googleCalendarService } from '@/services/googleCalendar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Spinner } from '@/components/common/Spinner';

interface CalendarSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

/**
 * Composant pour configurer les paramètres du calendrier
 */
const CalendarSettingsComponent: React.FC<CalendarSettingsProps> = ({ isOpen, onClose, onSave }) => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<CalendarSettings | null>(null);
  const [calendars, setCalendars] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    defaultCalendarId: 'primary',
    enableNotifications: true,
    notificationMinutes: [15, 60],
    autoSyncEnabled: true,
    syncInterval: 60
  });

  // Chargement des paramètres et calendriers
  useEffect(() => {
    if (!user || !isOpen) return;

    const loadData = async () => {
      setIsLoading(true);
      try {
        // Chargement des paramètres existants
        const userSettings = await getOrCreateCalendarSettings(user.uid);
        setSettings(userSettings);
        setFormData({
          defaultCalendarId: userSettings.defaultCalendarId,
          enableNotifications: userSettings.enableNotifications,
          notificationMinutes: userSettings.notificationMinutes,
          autoSyncEnabled: userSettings.autoSyncEnabled,
          syncInterval: userSettings.syncInterval
        });

        // Chargement des calendriers Google
        try {
          const userCalendars = await googleCalendarService.getCalendars();
          setCalendars(userCalendars);
        } catch (error) {
          console.error('Erreur lors du chargement des calendriers:', error);
          // Utiliser le calendrier principal par défaut
          setCalendars([{ id: 'primary', summary: 'Calendrier principal' }]);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [user, isOpen]);

  // Gestion de la sauvegarde
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || isSaving) return;

    setIsSaving(true);
    try {
      await updateCalendarSettings(user.uid, formData);
      onSave();
      onClose();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Gestion des notifications multiples
  const handleNotificationChange = (minutes: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      notificationMinutes: checked
        ? [...prev.notificationMinutes, minutes].sort((a, b) => a - b)
        : prev.notificationMinutes.filter(m => m !== minutes)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>Paramètres du Calendrier</CardTitle>
        </CardHeader>
        
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Spinner size="lg" />
            </div>
          ) : (
            <form onSubmit={handleSave} className="space-y-6">
              {/* Calendrier par défaut */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Calendrier par défaut
                </label>
                <select
                  value={formData.defaultCalendarId}
                  onChange={(e) => setFormData(prev => ({ ...prev, defaultCalendarId: e.target.value }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {calendars.map((calendar) => (
                    <option key={calendar.id} value={calendar.id}>
                      {calendar.summary || calendar.id}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Les nouveaux événements seront créés dans ce calendrier
                </p>
              </div>

              {/* Notifications */}
              <div>
                <label className="flex items-center space-x-2 mb-3">
                  <input
                    type="checkbox"
                    checked={formData.enableNotifications}
                    onChange={(e) => setFormData(prev => ({ ...prev, enableNotifications: e.target.checked }))}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Activer les notifications</span>
                </label>

                {formData.enableNotifications && (
                  <div className="ml-6 space-y-2">
                    <p className="text-sm text-gray-600 mb-2">Recevoir des rappels avant les événements :</p>
                    
                    {[
                      { minutes: 15, label: '15 minutes avant' },
                      { minutes: 60, label: '1 heure avant' },
                      { minutes: 1440, label: '1 jour avant' },
                      { minutes: 10080, label: '1 semaine avant' }
                    ].map(({ minutes, label }) => (
                      <label key={minutes} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.notificationMinutes.includes(minutes)}
                          onChange={(e) => handleNotificationChange(minutes, e.target.checked)}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                        <span className="text-sm text-gray-700">{label}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Synchronisation automatique */}
              <div>
                <label className="flex items-center space-x-2 mb-3">
                  <input
                    type="checkbox"
                    checked={formData.autoSyncEnabled}
                    onChange={(e) => setFormData(prev => ({ ...prev, autoSyncEnabled: e.target.checked }))}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Synchronisation automatique</span>
                </label>

                {formData.autoSyncEnabled && (
                  <div className="ml-6">
                    <label className="block text-sm text-gray-600 mb-1">
                      Intervalle de synchronisation (minutes)
                    </label>
                    <select
                      value={formData.syncInterval}
                      onChange={(e) => setFormData(prev => ({ ...prev, syncInterval: parseInt(e.target.value) }))}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value={15}>15 minutes</option>
                      <option value={30}>30 minutes</option>
                      <option value={60}>1 heure</option>
                      <option value={120}>2 heures</option>
                      <option value={360}>6 heures</option>
                      <option value={720}>12 heures</option>
                      <option value={1440}>24 heures</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Fréquence de synchronisation entre Firebase et Google Calendar
                    </p>
                  </div>
                )}
              </div>

              {/* Informations de synchronisation */}
              {settings && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Informations de synchronisation</h4>
                  <p className="text-xs text-gray-600">
                    Dernière synchronisation : {settings.lastSyncDate.toLocaleString('fr-FR')}
                  </p>
                </div>
              )}

              {/* Boutons d'action */}
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSaving}
                >
                  Annuler
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving}
                >
                  {isSaving ? 'Sauvegarde...' : 'Sauvegarder'}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CalendarSettingsComponent;
